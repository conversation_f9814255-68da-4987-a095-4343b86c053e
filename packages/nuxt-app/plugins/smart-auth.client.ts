/**
 * 智能认证插件
 * 替换原有的重复认证逻辑，使用新的TokenManager
 */

export default defineNuxtPlugin(async () => {
  const { ensureValidToken, checkTokenStatus, getStoredUserInfo } =
    useTokenManager()
  const userStore = useUserStore()

  // 用户状态初始化已在 app.vue 中统一处理，这里只负责token验证和更新

  // 异步验证和更新token，但不阻塞页面渲染
  Promise.resolve().then(async () => {
    try {
      // 检查当前token状态
      const tokenStatus = await checkTokenStatus()

      if (tokenStatus.status === 'valid') {
        // Token有效，更新用户状态
        if (tokenStatus.token) {
          userStore.setToken(tokenStatus.token)
        }

        // 主动获取最新的用户信息（包括最新的钻石数量）
        try {
          await userStore.getUserInfo()
          console.log('✅ 用户信息已更新，包括最新钻石数量')
        } catch (error) {
          console.warn('⚠️ 获取最新用户信息失败，使用缓存数据:', error)
          // 如果API调用失败，降级使用存储的用户信息
          const userInfo = getStoredUserInfo()
          if (userInfo) {
            userStore.setUserInfo(userInfo as any)
          }
        }
      } else {
        // Token无效或不存在，确保获取有效token
        const { token, isNewToken } = await ensureValidToken()

        if (token) {
          userStore.setToken(token)

          // 获取最新的用户信息
          try {
            await userStore.getUserInfo()
            console.log('✅ 新token获取用户信息成功')
          } catch (error) {
            console.warn('⚠️ 新token获取用户信息失败:', error)
            // 如果API调用失败，降级使用存储的用户信息
            if (isNewToken) {
              const userInfo = getStoredUserInfo()
              if (userInfo) {
                userStore.setUserInfo(userInfo as any)
              }
            }
          }
        }
      }
    } catch (error) {
      console.error('Smart auth initialization failed:', error)
      // 认证失败时的处理已经在 ensureValidToken 中完成
    }
  })
})
