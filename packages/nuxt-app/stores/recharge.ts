import { defineStore } from 'pinia'
import { ref } from 'vue'
import {
  createPaymentAPI,
  type PriceItem,
  getProjectConfig,
} from 'shared-payment'

export const useRechargeStore = defineStore(
  'recharge',
  () => {
    const config = useRuntimeConfig()
    const { getExistingToken } = useTokenManager()
    const loadingStore = useLoadingStore()

    // 获取支付提供商调试参数
    const paymentProvider = config.public.paymentProvider as string

    // 获取项目配置
    const projectConfig = getProjectConfig()
    console.log('nuxt-app recharge store - 项目配置:', projectConfig)

    // 创建支付API实例，并传递支付提供商参数和 Stripe 公钥
    const paymentAPI = createPaymentAPI(
      config.public.apiBase,
      () => getExistingToken(),
      {
        debugPaymentProvider: paymentProvider,
        stripePublicKey: config.public.stripePublicKey,
      },
    )

    // State
    const visible = ref(false)
    const priceList = ref<PriceItem[]>([])
    const loading = ref(false)
    const error = ref<string | null>(null)
    const paymentLoading = ref(false)

    // Actions
    const fetchPriceList = async () => {
      loading.value = true
      error.value = null

      try {
        const data = await paymentAPI.getPriceList()
        priceList.value = data
      } catch (err) {
        error.value =
          err instanceof Error ? err.message : 'Failed to fetch price list'
        throw err
      } finally {
        loading.value = false
      }
    }

    /**
     * 创建支付并跳转
     */
    const createAndRedirectToPayment = async (
      priceId: string,
    ): Promise<void> => {
      paymentLoading.value = true
      error.value = null

      // 显示全局支付loading
      loadingStore.showPaymentLoading('Processing your payment...')

      try {
        // 使用 shared-payment 的统一方法，内部已处理所有错误逻辑
        await paymentAPI.createAndRedirectToPayment({
          priceId,
          successUrl: generateSuccessUrl(),
          cancelUrl: generateCancelUrl(),
        })
        // 成功时会自动重定向，不需要额外处理
      } finally {
        paymentLoading.value = false
        loadingStore.hideLoading()
      }
    }

    const showRechargeModal = () => {
      visible.value = true
    }

    const hideRechargeModal = () => {
      visible.value = false
    }

    const toggleRechargeModal = () => {
      visible.value = !visible.value
    }

    const reset = () => {
      priceList.value = []
      error.value = null
      paymentLoading.value = false
    }

    // 生成成功回调URL
    const generateSuccessUrl = (): string => {
      if (typeof window !== 'undefined') {
        const baseUrl = window.location.origin

        // 根据项目配置决定是否添加 Stripe 参数
        if (projectConfig.paymentProvider === 'stripe') {
          // 为 Stripe 支付添加预设参数，{CHECKOUT_SESSION_ID} 会被 Stripe 自动替换
          const successUrl = `${baseUrl}/recharge-success?provider=stripe&session_id={CHECKOUT_SESSION_ID}`
          console.log(
            'nuxt-app recharge store - 生成 Stripe success URL:',
            successUrl,
          )
          return successUrl
        } else {
          // OnlyPay 使用基础 URL
          const successUrl = `${baseUrl}/recharge-success`
          console.log(
            'nuxt-app recharge store - 生成 OnlyPay success URL:',
            successUrl,
          )
          return successUrl
        }
      }
      return ''
    }

    // 生成取消回调URL
    const generateCancelUrl = (): string => {
      if (typeof window !== 'undefined') {
        return window.location.href
      }
      return ''
    }

    return {
      // State
      visible,
      priceList,
      loading,
      error,
      paymentLoading,

      // Computed
      projectConfig: readonly(ref(projectConfig)),

      // Actions
      fetchPriceList,
      createAndRedirectToPayment,
      showRechargeModal,
      hideRechargeModal,
      toggleRechargeModal,
      reset,
    }
  },
  {
    persist: false,
  },
)
